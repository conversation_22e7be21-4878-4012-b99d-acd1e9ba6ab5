(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__47f0086d._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/messages/en.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"Common\":{\"brandName\":\"PersonaRoll\",\"tagline\":\"Authentic Voices, Infinite Stories\",\"getEarlyAccess\":\"Get Early Access\",\"watchDemo\":\"Watch Demo\",\"learnMore\":\"Learn More\"},\"Navigation\":{\"home\":\"Home\",\"about\":\"About\",\"login\":\"Login / Sign Up with Google\",\"privacy\":\"Privacy\",\"terms\":\"Terms\",\"contact\":\"Contact\",\"twitter\":\"Twitter\"},\"Hero\":{\"badge\":\"AUTO PILOT ON SOCIAL MEDIA\",\"title\":\"Go viral — just by being you. Automatically.\",\"subtitle\":\"PersonaRoll is always on trend\",\"description\":\"Turn your photos into viral content with AI personalities that understand your style and current trends.\",\"stats\":{\"ai\":\"AI\",\"aiLabel\":\"Personality creation\",\"live\":\"Live\",\"liveLabel\":\"Social feed analysis\",\"infinite\":\"∞\",\"infiniteLabel\":\"Memory retention\"}},\"Features\":{\"sectionTitle\":\"The Complete Photo-to-Post Workflow\",\"sectionSubtitle\":\"From your camera roll to viral content in three simple steps\",\"step1\":{\"title\":\"Step 1: Upload Your Photos\",\"description\":\"Start with any photo from your camera roll. Our AI instantly analyzes your visuals and matches them with trending topics and hashtags.\",\"features\":[\"Upload photos from any moment\",\"AI matches photos to current trends\",\"Your visuals drive the content strategy\"]},\"step2\":{\"title\":\"Step 2: Choose Your Voice\",\"description\":\"Select from AI personalities, each with unique knowledge sources and writing styles. From fashion-forward to foodie expert.\",\"features\":[\"Multiple AI personalities to choose from\",\"Each reads custom knowledge sources\",\"Maintains consistent voice and tone\"]},\"step3\":{\"title\":\"Step 3: Get Your Post\",\"description\":\"Receive ready-to-publish content that matches your photo, follows current trends, and speaks in your chosen voice.\",\"features\":[\"Platform-optimized content\",\"Trending hashtags included\",\"Ready to publish instantly\"]},\"smartKnowledge\":{\"title\":\"Smart Knowledge Sources\",\"description\":\"Each persona reads from curated sources to stay current with their expertise area.\"},\"alwaysOnTrend\":{\"title\":\"Always On-Trend\",\"description\":\"Real-time analysis of social media trends ensures your content is always relevant.\"},\"performanceInsights\":{\"title\":\"Performance Insights\",\"description\":\"Track what works and optimize your content strategy with detailed analytics.\"}},\"Screenshots\":{\"sectionTitle\":\"See PersonaRoll In Action\",\"sectionSubtitle\":\"A glimpse into your new content creation workflow\",\"cameraRoll\":{\"title\":\"Camera Roll Analyzer\",\"tip\":\"AI analyzes photos for trending content\",\"filters\":{\"all\":\"All (24)\",\"fashion\":\"Fashion (8)\",\"lifestyle\":\"Lifestyle (6)\",\"food\":\"Food (4)\",\"travel\":\"Travel (6)\"}},\"photoDetails\":{\"autumnOutfit\":{\"filename\":\"autumn_outfit_2024.jpg\",\"description\":\"Cozy autumn sweater with matching accessories, perfect for fall weather trends\",\"date\":\"Oct 28, 2024\",\"status\":\"Used\",\"tags\":[\"autumn\",\"fashion\",\"cozy\"],\"type\":\"Fashion\"},\"morningCoffee\":{\"filename\":\"morning_coffee.jpg\",\"description\":\"Perfect morning coffee setup with natural lighting\",\"date\":\"Oct 27, 2024\",\"status\":\"Unused\",\"tags\":[\"coffee\",\"morning\"],\"type\":\"Lifestyle\"}},\"startWithPhotos\":{\"title\":\"Start with Your Photos\",\"description\":\"Upload any photo and watch AI instantly match it with trending topics. Your visuals drive the content, not the other way around.\"},\"personasWithKnowledge\":{\"title\":\"Personas with Custom Knowledge\",\"description\":\"Each persona reads different sources and writes in their unique style. Fashion Fiona follows fashion feeds, while Foodie Fred reads culinary blogs.\"},\"smartContentGeneration\":{\"title\":\"Smart Content Generation\",\"description\":\"AI analyzes your photo, matches it with current trends, then generates content in your chosen persona's unique voice and style.\"}},\"Personas\":{\"fashionFiona\":{\"name\":\"Fashion Fiona\",\"type\":\"ENFP • Fashion & Lifestyle\",\"description\":\"A trendy fashion enthusiast who stays on top of the latest style movements and seasonal trends.\",\"knowledge\":[\"Fashion\",\"Style\",\"Trends\"],\"sources\":[\"Vogue RSS\",\"Fashion Week\",\"Style Blogs\",\"Beauty Tips\"]},\"chefMarcus\":{\"name\":\"Chef Marcus\",\"type\":\"ISFJ • Culinary Expert\",\"description\":\"A passionate chef who creates engaging food content with expert culinary knowledge.\",\"knowledge\":[\"Recipes\",\"Cooking\",\"Food\"]},\"fashionQianqian\":{\"name\":\"Fashion Qianqian\",\"type\":\"ENFP • Trendy Voice\",\"description\":\"A fashion-forward personality with a fresh perspective on style trends.\"}},\"ContentGeneration\":{\"steps\":{\"photoAnalysis\":\"Photo Analysis\",\"trendMatch\":\"Trend Match\",\"generate\":\"Generate\"},\"badges\":{\"fashionFiona\":\"Fashion Fiona\",\"autumnTrends\":\"Autumn Trends\"},\"sampleContent\":\"OMG this autumn look is EVERYTHING! 😍 The oversized knit + ankle boots combo is absolutely perfect for the cozy fall vibes we're all craving right now!\",\"previewContent\":\"Just discovered the perfect autumn outfit combo! 🍂\",\"aiGenerated\":\"AI Generated • Ready to publish\"},\"CTA\":{\"title\":\"Turn Your Photos Into Viral Content\",\"subtitle\":\"Join the waitlist and be the first to experience photo-first content creation\",\"emailPlaceholder\":\"Enter your email\",\"note\":\"🎉 First 1000 users get 3 months free\",\"successTitle\":\"Thanks for joining!\",\"successMessage\":\"We'll notify you when PersonaRoll launches.\"},\"Footer\":{\"copyright\":\"© 2024 PersonaRoll. All rights reserved.\"},\"LanguageSwitcher\":{\"english\":\"English\",\"chinese\":\"中文\",\"japanese\":\"日本語\"}}"));}),
"[project]/messages/ja.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"Common\":{\"brandName\":\"PersonaRoll\",\"tagline\":\"本物の声、無限の物語\",\"getEarlyAccess\":\"早期アクセスを取得\",\"watchDemo\":\"デモを見る\",\"learnMore\":\"詳細を見る\"},\"Navigation\":{\"home\":\"ホーム\",\"about\":\"アバウト\",\"login\":\"Googleでログイン/サインアップ\",\"privacy\":\"プライバシー\",\"terms\":\"利用規約\",\"contact\":\"お問い合わせ\",\"twitter\":\"Twitter\"},\"Hero\":{\"badge\":\"ソーシャルメディアオートパイロット\",\"title\":\"あなたらしくいるだけでバズる — 全自動で。\",\"subtitle\":\"PersonaRollは常にトレンドに乗っている\",\"description\":\"あなたのスタイルと現在のトレンドを理解するAIペルソナで、写真をバイラルコンテンツに変換します。\",\"stats\":{\"ai\":\"AI\",\"aiLabel\":\"ペルソナ作成\",\"live\":\"ライブ\",\"liveLabel\":\"ソーシャルフィード分析\",\"infinite\":\"∞\",\"infiniteLabel\":\"メモリ保持\"}},\"Features\":{\"sectionTitle\":\"完全な写真から投稿までのワークフロー\",\"sectionSubtitle\":\"カメラロールからバイラルコンテンツまで、3つの簡単なステップで\",\"step1\":{\"title\":\"ステップ1：写真をアップロード\",\"description\":\"カメラロールから任意の写真を選択します。私たちのAIがあなたのビジュアルを即座に分析し、トレンドトピックやハッシュタグとマッチングします。\",\"features\":[\"いつでもの写真をアップロード\",\"AIが写真を現在のトレンドとマッチング\",\"あなたのビジュアルがコンテンツ戦略を主導\"]},\"step2\":{\"title\":\"ステップ2：あなたの声を選択\",\"description\":\"AIペルソナから選択します。それぞれが独自の知識ソースとライティングスタイルを持っています。ファッション最先端からグルメ専門家まで。\",\"features\":[\"複数のAIペルソナから選択可能\",\"それぞれがカスタム知識ソースを読み取り\",\"一貫した声とトーンを維持\"]},\"step3\":{\"title\":\"ステップ3：投稿を取得\",\"description\":\"あなたの写真にマッチし、現在のトレンドに従い、選択した声で話す、すぐに公開できるコンテンツを受け取ります。\",\"features\":[\"プラットフォーム最適化コンテンツ\",\"トレンドハッシュタグを含む\",\"すぐに公開可能\"]},\"smartKnowledge\":{\"title\":\"スマート知識ソース\",\"description\":\"各ペルソナは厳選されたソースから読み取り、その専門分野の最新情報を維持します。\"},\"alwaysOnTrend\":{\"title\":\"常にトレンドに乗っている\",\"description\":\"ソーシャルメディアトレンドのリアルタイム分析で、あなたのコンテンツが常に関連性を保つことを保証します。\"},\"performanceInsights\":{\"title\":\"パフォーマンスインサイト\",\"description\":\"詳細な分析で効果的なコンテンツを追跡し、コンテンツ戦略を最適化します。\"}},\"Screenshots\":{\"sectionTitle\":\"PersonaRollの実際の動作を見る\",\"sectionSubtitle\":\"新しいコンテンツ作成ワークフローを一瞥\",\"cameraRoll\":{\"title\":\"カメラロールアナライザー\",\"tip\":\"AIが写真を分析してトレンドコンテンツを探します\",\"filters\":{\"all\":\"すべて (24)\",\"fashion\":\"ファッション (8)\",\"lifestyle\":\"ライフスタイル (6)\",\"food\":\"フード (4)\",\"travel\":\"旅行 (6)\"}},\"photoDetails\":{\"autumnOutfit\":{\"filename\":\"autumn_outfit_2024.jpg\",\"description\":\"秋の天気トレンドにピッタリの、小物と合わせた心地よい秋セーター\",\"date\":\"2024年10月28日\",\"status\":\"使用済み\",\"tags\":[\"秋\",\"ファッション\",\"心地よい\"],\"type\":\"ファッション\"},\"morningCoffee\":{\"filename\":\"morning_coffee.jpg\",\"description\":\"自然光での完璧な朝のコーヒーセットアップ\",\"date\":\"2024年10月27日\",\"status\":\"未使用\",\"tags\":[\"コーヒー\",\"朝\"],\"type\":\"ライフスタイル\"}},\"startWithPhotos\":{\"title\":\"あなたの写真から始める\",\"description\":\"任意の写真をアップロードし、AIが即座にトレンドトピックとマッチングする様子を見てください。あなたのビジュアルがコンテンツを主導し、その逆ではありません。\"},\"personasWithKnowledge\":{\"title\":\"カスタム知識を持つペルソナ\",\"description\":\"各ペルソナは異なるソースを読み取り、それぞれの独自のスタイルで書きます。ファッションフィオナはファッションフィードをフォローし、グルメフレッドは料理ブログを読みます。\"},\"smartContentGeneration\":{\"title\":\"スマートコンテンツ生成\",\"description\":\"AIがあなたの写真を分析し、現在のトレンドとマッチングさせ、選択したペルソナの独自の声とスタイルでコンテンツを生成します。\"}},\"Personas\":{\"fashionFiona\":{\"name\":\"ファッションフィオナ\",\"type\":\"ENFP • ファッション＆ライフスタイル\",\"description\":\"最新のスタイル動向とシーズントレンドに精通したトレンディなファッション愛好家。\",\"knowledge\":[\"ファッション\",\"スタイル\",\"トレンド\"],\"sources\":[\"Vogue RSS\",\"ファッションウィーク\",\"スタイルブログ\",\"ビューティーティップス\"]},\"chefMarcus\":{\"name\":\"シェフマーカス\",\"type\":\"ISFJ • 料理専門家\",\"description\":\"専門的な料理知識で魅力的なフードコンテンツを作る情熱的なシェフ。\",\"knowledge\":[\"レシピ\",\"料理\",\"フード\"]},\"fashionQianqian\":{\"name\":\"ファッションチェンチェン\",\"type\":\"ENFP • トレンディボイス\",\"description\":\"スタイルトレンドに新鮮な視点を持つファッション最先端のペルソナ。\"}},\"ContentGeneration\":{\"steps\":{\"photoAnalysis\":\"写真分析\",\"trendMatch\":\"トレンドマッチング\",\"generate\":\"生成\"},\"badges\":{\"fashionFiona\":\"ファッションフィオナ\",\"autumnTrends\":\"秋のトレンド\"},\"sampleContent\":\"うわぁ、この秋のルック最高！😍 オーバーサイズニット+アンクルブーツの組み合わせは、今みんなが求めている心地よい秋の雰囲気に絶対ピッタリ！\",\"previewContent\":\"完璧な秋のコーディネートを発見！🍂\",\"aiGenerated\":\"AI生成 • 公開準備完了\"},\"CTA\":{\"title\":\"あなたの写真をバイラルコンテンツに変換\",\"subtitle\":\"ウェイティングリストに参加し、写真ファーストコンテンツ作成を最初に体験してください\",\"emailPlaceholder\":\"メールアドレスを入力\",\"note\":\"🎉 最初の1000人のユーザーは3ヶ月無料\",\"successTitle\":\"ご参加ありがとうございます！\",\"successMessage\":\"PersonaRollがローンチされたらお知らせします。\"},\"Footer\":{\"copyright\":\"© 2024 PersonaRoll. すべての権利を保有します。\"},\"LanguageSwitcher\":{\"english\":\"English\",\"chinese\":\"中文\",\"japanese\":\"日本語\"}}"));}),
"[project]/messages/zh.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"Common\":{\"brandName\":\"PersonaRoll\",\"tagline\":\"真实声音，无限故事\",\"getEarlyAccess\":\"获取早期访问\",\"watchDemo\":\"观看演示\",\"learnMore\":\"了解更多\"},\"Navigation\":{\"home\":\"首页\",\"about\":\"关于\",\"login\":\"使用 Google 登录/注册\",\"privacy\":\"隐私\",\"terms\":\"条款\",\"contact\":\"联系\",\"twitter\":\"推特\"},\"Hero\":{\"badge\":\"社交媒体自动驾驶\",\"title\":\"做自己就能火 — 全自动。\",\"subtitle\":\"PersonaRoll 永远紧跟潮流\",\"description\":\"用理解你风格和当前趋势的AI人格，将你的照片转化为病毒式内容。\",\"stats\":{\"ai\":\"AI\",\"aiLabel\":\"人格创建\",\"live\":\"实时\",\"liveLabel\":\"社交动态分析\",\"infinite\":\"∞\",\"infiniteLabel\":\"记忆保持\"}},\"Features\":{\"sectionTitle\":\"完整的照片到帖子工作流\",\"sectionSubtitle\":\"从相机胶卷到病毒式内容，只需三个简单步骤\",\"step1\":{\"title\":\"第一步：上传你的照片\",\"description\":\"从相机胶卷中选择任何照片。我们的AI立即分析你的视觉内容，并将其与热门话题和标签匹配。\",\"features\":[\"上传任何时刻的照片\",\"AI将照片与当前趋势匹配\",\"你的视觉内容驱动内容策略\"]},\"step2\":{\"title\":\"第二步：选择你的声音\",\"description\":\"从AI人格中选择，每个都有独特的知识来源和写作风格。从时尚前沿到美食专家。\",\"features\":[\"多个AI人格可供选择\",\"每个都读取自定义知识来源\",\"保持一致的声音和语调\"]},\"step3\":{\"title\":\"第三步：获取你的帖子\",\"description\":\"接收与你的照片匹配、遵循当前趋势、并以你选择的声音说话的即发布内容。\",\"features\":[\"平台优化内容\",\"包含热门标签\",\"立即可发布\"]},\"smartKnowledge\":{\"title\":\"智能知识来源\",\"description\":\"每个人格都从精选来源阅读，以保持其专业领域的最新状态。\"},\"alwaysOnTrend\":{\"title\":\"永远紧跟潮流\",\"description\":\"实时分析社交媒体趋势，确保你的内容始终相关。\"},\"performanceInsights\":{\"title\":\"性能洞察\",\"description\":\"通过详细分析跟踪有效内容并优化你的内容策略。\"}},\"Screenshots\":{\"sectionTitle\":\"看看 PersonaRoll 的实际效果\",\"sectionSubtitle\":\"一窥你的新内容创作工作流\",\"cameraRoll\":{\"title\":\"相机胶卷分析器\",\"tip\":\"AI分析照片寻找热门内容\",\"filters\":{\"all\":\"全部 (24)\",\"fashion\":\"时尚 (8)\",\"lifestyle\":\"生活方式 (6)\",\"food\":\"美食 (4)\",\"travel\":\"旅行 (6)\"}},\"photoDetails\":{\"autumnOutfit\":{\"filename\":\"autumn_outfit_2024.jpg\",\"description\":\"舒适的秋季毛衣配搭配饰，完美适合秋季天气趋势\",\"date\":\"2024年10月28日\",\"status\":\"已使用\",\"tags\":[\"秋季\",\"时尚\",\"舒适\"],\"type\":\"时尚\"},\"morningCoffee\":{\"filename\":\"morning_coffee.jpg\",\"description\":\"完美的晨间咖啡设置，自然光线\",\"date\":\"2024年10月27日\",\"status\":\"未使用\",\"tags\":[\"咖啡\",\"早晨\"],\"type\":\"生活方式\"}},\"startWithPhotos\":{\"title\":\"从你的照片开始\",\"description\":\"上传任何照片，观看AI立即将其与热门话题匹配。你的视觉内容驱动内容，而不是相反。\"},\"personasWithKnowledge\":{\"title\":\"具有自定义知识的人格\",\"description\":\"每个人格都读取不同的来源，并以其独特的风格写作。时尚菲奥娜关注时尚动态，而美食弗雷德阅读烹饪博客。\"},\"smartContentGeneration\":{\"title\":\"智能内容生成\",\"description\":\"AI分析你的照片，将其与当前趋势匹配，然后以你选择的人格独特声音和风格生成内容。\"}},\"Personas\":{\"fashionFiona\":{\"name\":\"时尚菲奥娜\",\"type\":\"ENFP • 时尚与生活方式\",\"description\":\"一个紧跟最新风格动向和季节趋势的时尚爱好者。\",\"knowledge\":[\"时尚\",\"风格\",\"趋势\"],\"sources\":[\"Vogue RSS\",\"时装周\",\"风格博客\",\"美容贴士\"]},\"chefMarcus\":{\"name\":\"主厨马库斯\",\"type\":\"ISFJ • 烹饪专家\",\"description\":\"一个充满激情的厨师，用专业烹饪知识创造引人入胜的美食内容。\",\"knowledge\":[\"食谱\",\"烹饪\",\"美食\"]},\"fashionQianqian\":{\"name\":\"时尚倩倩\",\"type\":\"ENFP • 潮流声音\",\"description\":\"一个对风格趋势有新鲜视角的时尚前沿人格。\"}},\"ContentGeneration\":{\"steps\":{\"photoAnalysis\":\"照片分析\",\"trendMatch\":\"趋势匹配\",\"generate\":\"生成\"},\"badges\":{\"fashionFiona\":\"时尚菲奥娜\",\"autumnTrends\":\"秋季趋势\"},\"sampleContent\":\"天哪，这个秋季造型太棒了！😍 宽松针织衫+踝靴的组合绝对完美，正是我们现在都渴望的舒适秋日氛围！\",\"previewContent\":\"刚发现完美的秋季服装搭配！🍂\",\"aiGenerated\":\"AI生成 • 准备发布\"},\"CTA\":{\"title\":\"将你的照片转化为病毒式内容\",\"subtitle\":\"加入等待名单，成为第一批体验照片优先内容创作的用户\",\"emailPlaceholder\":\"输入你的邮箱\",\"note\":\"🎉 前1000名用户免费使用3个月\",\"successTitle\":\"感谢加入！\",\"successMessage\":\"PersonaRoll上线时我们会通知你。\"},\"Footer\":{\"copyright\":\"© 2024 PersonaRoll. 保留所有权利。\"},\"LanguageSwitcher\":{\"english\":\"English\",\"chinese\":\"中文\",\"japanese\":\"日本語\"}}"));}),
"[project]/src/i18n/request.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js [middleware-edge] (ecmascript) <export default as getRequestConfig>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/routing.ts [middleware-edge] (ecmascript)");
;
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__["getRequestConfig"])(async ({ requestLocale })=>{
    // This typically corresponds to the `[locale]` segment
    let locale = await requestLocale;
    // Ensure that a valid locale is used
    if (!locale || !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["routing"].locales.includes(locale)) {
        locale = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["routing"].defaultLocale;
    }
    return {
        locale,
        messages: (await __turbopack_context__.f({
            "../../messages/en.json": {
                id: ()=>"[project]/messages/en.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/messages/en.json (json)"))
            },
            "../../messages/ja.json": {
                id: ()=>"[project]/messages/ja.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/messages/ja.json (json)"))
            },
            "../../messages/zh.json": {
                id: ()=>"[project]/messages/zh.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/messages/zh.json (json)"))
            }
        }).import(`../../messages/${locale}.json`)).default
    };
});
}),
"[project]/src/i18n/routing.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Link": ()=>Link,
    "redirect": ()=>redirect,
    "routing": ()=>routing,
    "usePathname": ()=>usePathname,
    "useRouter": ()=>useRouter
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$routing$2f$defineRouting$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__defineRouting$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/routing/defineRouting.js [middleware-edge] (ecmascript) <export default as defineRouting>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$navigation$2f$react$2d$server$2f$createNavigation$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__createNavigation$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/navigation/react-server/createNavigation.js [middleware-edge] (ecmascript) <export default as createNavigation>");
;
;
const routing = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$routing$2f$defineRouting$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__defineRouting$3e$__["defineRouting"])({
    // A list of all locales that are supported
    locales: [
        "en",
        "zh",
        "ja"
    ],
    // Used when no locale matches
    defaultLocale: "en",
    // The `pathnames` object holds pairs of internal and
    // external paths. Based on the locale, the external
    // paths are rewritten to the shared, internal ones.
    pathnames: {
        // If all locales use the same pathname, a single
        // external path can be provided for all locales
        "/": "/",
        "/about": {
            en: "/about",
            zh: "/about",
            ja: "/about"
        }
    }
});
const { Link, redirect, usePathname, useRouter } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$navigation$2f$react$2d$server$2f$createNavigation$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__createNavigation$3e$__["createNavigation"])(routing);
}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "config": ()=>config,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/middleware/middleware.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/routing.ts [middleware-edge] (ecmascript)");
;
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["routing"]);
const config = {
    // Match only internationalized pathnames
    matcher: [
        '/',
        '/(zh|en|ja)/:path*'
    ]
};
}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__47f0086d._.js.map