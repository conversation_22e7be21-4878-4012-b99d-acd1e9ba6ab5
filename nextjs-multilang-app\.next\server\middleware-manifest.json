{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_cd76b067._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_75a74226._.js", "server/edge/chunks/[root-of-the-server]__47f0086d._.js", "server/edge/chunks/edge-wrapper_be163fe5.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(zh|en|ja))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(zh|en|ja)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "CPMI/zEbCknzvIHZWmaMvRokN9Bs87M7zmty+a43oIE=", "__NEXT_PREVIEW_MODE_ID": "035394ffa82c5226aba8d8dee4bc6dcf", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1bca376a4db4c422c9c23db2ddf78235fbcc9696619444cb5f70e0653d269847", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "71676e3201116e168d889e0f8a868fca287add43682d1a78585e8e5d5ed061e1"}}}, "sortedMiddleware": ["/"], "functions": {}}